<!--
  待服务清单组件
  支持切换流程类型：待勘查、待安装、待回收
-->
<script setup lang="ts">
import type { FlowItem } from '@shenanPioneer/api'
import { fetchFlowByType, fetchPendingInspection, fetchPendingInstallation, fetchPendingRecycling } from '@shenanPioneer/api'
import { FormatDate } from '@shencom/utils'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { FlowType } from '@/modules/shenanPioneer/enum'
import Card from './Card.vue'

const cacheData = new Map<FlowType, SC.API.IndexInterface<FlowItem>>()

// 路由实例 (暂时未使用，保留用于后续路由跳转功能)
const _router = useRouter()

// 响应式数据
const loading = ref(false)
const currentTab = ref<FlowType>(FlowType.现场勘察)
const dataList = ref<FlowItem[]>([])
const total = ref(0)

// 标签页配置
const tabs = [
  { value: FlowType.现场勘察, label: '待勘查', icon: 'i-material-symbols:folder-eye' },
  { value: FlowType.上门安装, label: '待安装', icon: 'i-material-symbols:construction' },
  { value: FlowType.上门回收, label: '待回收', icon: 'i-material-symbols:recycling' },
]

watch(currentTab, () => {
  fetchCurrentTabData()
})

/**
 * 获取当前标签页数据
 */
async function fetchCurrentTabData() {
  loading.value = true
  try {
    let response = null

    if (cacheData.has(currentTab.value)) {
      response = cacheData.get(currentTab.value)
    }
    else {
      response = await fetchFlowByType(currentTab.value)
    }

    if (response) {
      dataList.value = response.content
      total.value = response.totalElements
      cacheData.set(currentTab.value, response)
    }
  }
  catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
    dataList.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 处理服务项点击
 * @param item - 服务项
 */
function handleClick(item: FlowItem) {
  // 处理服务项点击事件
  console.log('点击服务项:', item)
}

/**
 * 处理更多点击
 */
function handleMoreClick() {
  // 处理更多点击
  console.log('点击更多 - 待服务清单')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCurrentTabData()
})
</script>

<template>
  <Card title="待服务清单">
    <template #header>
      <div
        class="flex cursor-pointer items-center justify-center rounded px-3 py-1 text-sm text-primary transition-colors duration-300 hover:bg-primary/10"
        @click="handleMoreClick"
      >
        <span class="mr-1">更多</span>
        <FaIcon name="i-ep:arrow-right" />
      </div>
    </template>

    <!-- 标签页切换 -->
    <FaTabs v-model="currentTab" :list="tabs" class="mb-1 mt-3" />

    <!-- 数据列表 -->
    <div v-loading="loading" class="grid grid-cols-1 h-[350px] gap-3 overflow-y-auto">
      <div

        v-for="item in dataList" :key="item.id"
        dark=" bg-background" hover="border-blue-200 bg-blue/10 shadow-sm bg-primary/10"
        class="cursor-pointer border border-transparent rounded-md bg-primary-foreground p-3 transition-all transition-colors duration-200"
        @click="handleClick(item)"
      >
        <div class="flex justify-between">
          <div class="flex-1">
            <div class="text-lg text-foreground font-medium">
              {{ item.projectName }}
            </div>
            <div class="mt-1 flex items-center gap-1 text-xs text-secondary-foreground">
              <span class="text-xs">📍</span>
              <p class="truncate">
                {{ item.projectAddress }}
              </p>
            </div>
          </div>
          <div class="text-right">
            <div class="text-xs text-muted-foreground">
              {{ FormatDate(item.updatedAt, 'MM-DD HH:mm') }}
            </div>
          </div>
        </div>
      </div>

      <!-- 暂无数据状态 -->
      <div
        v-if="dataList.length === 0 && !loading"
        class="h-[350px] flex flex-col items-center justify-center text-muted-foreground"
      >
        <FaIcon name="i-ep:document" class="mb-3 text-4xl text-muted-foreground/50" />
        <div class="text-sm">
          暂无数据
        </div>
      </div>
    </div>
  </Card>
</template>
