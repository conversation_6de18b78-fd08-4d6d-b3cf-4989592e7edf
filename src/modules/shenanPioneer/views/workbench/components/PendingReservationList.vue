<!--
  待预约清单组件
  支持切换流程类型：待预约安装、待预约回收
-->
<script setup lang="ts">
import type { FlowItem } from '@shenanPioneer/api'
import { fetchFlowByCategory, FlowCategory } from '@shenanPioneer/api'
import { FormatDate } from '@shencom/utils'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { FlowType } from '@/modules/shenanPioneer/enum'
import Card from './Card.vue'

// 路由实例 (暂时未使用，保留用于后续路由跳转功能)
const _router = useRouter()

// 流程类型枚举
enum FlowTypeTab {
  INSTALLATION = 'installation',
  RECYCLING = 'recycling',
}

// 响应式数据
const loading = ref(false)
const currentTab = ref<FlowTypeTab>(FlowTypeTab.INSTALLATION)
const dataList = ref<FlowItem[]>([])
const total = ref(0)
const currentPage = ref(0)
const pageSize = ref(5) // 显示5条数据

// 标签页配置
const tabs = [
  { key: FlowTypeTab.INSTALLATION, label: '待预约安装' },
  { key: FlowTypeTab.RECYCLING, label: '待预约回收' },
]

/**
 * 获取当前标签页数据
 */
async function fetchCurrentTabData() {
  loading.value = true
  try {
    let category: FlowCategory
    if (currentTab.value === FlowTypeTab.INSTALLATION) {
      category = FlowCategory.PENDING_INSTALLATION_RESERVATION
    }
    else {
      category = FlowCategory.PENDING_RECYCLING_RESERVATION
    }

    const response = await fetchFlowByCategory(category, {
      size: pageSize.value,
      page: currentPage.value,
    })

    dataList.value = response.data.content
    total.value = response.data.totalElements
  }
  catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
    dataList.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 切换标签页
 * @param tab - 标签页类型
 */
function switchTab(tab: FlowTypeTab) {
  if (currentTab.value !== tab) {
    currentTab.value = tab
    currentPage.value = 0
    fetchCurrentTabData()
  }
}

/**
 * 处理预约项点击
 * @param item - 预约项
 */
function handleReservationClick(item: FlowItem) {
  // 处理预约项点击事件
  console.log('点击预约项:', item)
}

/**
 * 处理更多点击
 */
function handleMoreClick() {
  // 处理更多点击
  console.log('点击更多 - 待预约清单')
}

/**
 * 格式化流程类型显示
 */
function getFlowTypeLabel(flow: number): string {
  return FlowType[flow] || '未知'
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCurrentTabData()
})
</script>

<template>
  <Card title="待预约清单">
    <template #header>
      <div
        class="flex cursor-pointer items-center justify-center rounded px-3 py-1 text-sm text-primary transition-colors duration-300 hover:bg-primary/10"
        @click="handleMoreClick"
      >
        <span class="mr-1">更多</span>
        <FaIcon name="i-ep:arrow-right" />
      </div>
    </template>

    <!-- 标签页切换 -->
    <div class="mb-4 mt-2 flex rounded-lg bg-muted p-1 space-x-1">
      <button
        v-for="tab in tabs" :key="tab.key"
        class="flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors" :class="[
          currentTab === tab.key
            ? 'bg-background text-foreground shadow-sm'
            : 'text-muted-foreground hover:text-foreground',
        ]" @click="switchTab(tab.key)"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- 数据列表 -->
    <div v-loading="loading" class="min-h-[300px] space-y-3">
      <div
        v-for="item in dataList" :key="item.id"
        class="cursor-pointer border border-border rounded-md p-3 transition-colors hover:bg-muted/50"
        @click="handleReservationClick(item)"
      >
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="text-sm font-medium">
              {{ getFlowTypeLabel(item.flow) }}
            </div>
            <div class="mt-1 text-xs text-muted-foreground">
              项目ID: {{ item.projectId }}
            </div>
            <div class="mt-1 text-xs text-muted-foreground">
              订单ID: {{ item.orderId }}
            </div>
          </div>
          <div class="text-right">
            <div class="text-xs text-muted-foreground">
              {{ FormatDate(item.updatedAt, 'MM-DD HH:mm') }}
            </div>
          </div>
        </div>
      </div>

      <!-- 暂无数据状态 -->
      <div v-if="dataList.length === 0 && !loading" class="h-[300px] flex flex-col items-center justify-center text-muted-foreground">
        <FaIcon name="i-ep:document" class="mb-3 text-4xl text-muted-foreground/50" />
        <div class="text-sm">
          暂无数据
        </div>
      </div>
    </div>
  </Card>
</template>
