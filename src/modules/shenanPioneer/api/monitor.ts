/**
 * 流程监控相关 API
 */

import type { FlowItem, FlowParams } from './types'
import { http } from '@shencom/request'
import { url } from './config'

/**
 * 流程类型枚举
 */
export enum FlowCategory {
  /** 待预约安装 */
  PENDING_INSTALLATION_RESERVATION = 'pending_installation_reservation',
  /** 待预约回收 */
  PENDING_RECYCLING_RESERVATION = 'pending_recycling_reservation',
  /** 待勘查 */
  PENDING_INSPECTION = 'pending_inspection',
  /** 待安装 */
  PENDING_INSTALLATION = 'pending_installation',
  /** 待回收 */
  PENDING_RECYCLING = 'pending_recycling',
}

/**
 * 获取流程监控列表
 * @param params 请求参数
 * @returns 流程监控响应数据
 */
export async function fetchFlowList(params?: FlowParams) {
  const requestParams = {
    size: 10,
    page: 0,
    ...params,
  }

  return http.post<SC.API.IndexInterface<FlowItem>>(`${url}/monitor/flow/index`, requestParams)
}

/**
 * 获取特定类型的流程数据
 * @param category 流程类别
 * @param params 额外的请求参数
 * @returns 流程数据
 */
export async function fetchFlowByCategory(
  category: FlowCategory,
  params?: Omit<FlowParams, 'flow' | 'status'>,
) {
  const baseParams = {
    size: 5,
    page: 0,
    ...params,
  }

  let requestParams: FlowParams

  switch (category) {
    case FlowCategory.PENDING_INSTALLATION_RESERVATION:
      requestParams = {
        ...baseParams,
        flow: '0,1', // 工程创建,安装预约
      }
      break

    case FlowCategory.PENDING_RECYCLING_RESERVATION:
      requestParams = {
        ...baseParams,
        flow: '4,5', // 接入监管,施工完成
      }
      break

    case FlowCategory.PENDING_INSPECTION:
      requestParams = {
        ...baseParams,
        status: 0, // 未完成
        flow: '2', // 现场勘察
      }
      break

    case FlowCategory.PENDING_INSTALLATION:
      requestParams = {
        ...baseParams,
        status: 0, // 未完成
        flow: '3', // 上门安装
      }
      break

    case FlowCategory.PENDING_RECYCLING:
      requestParams = {
        ...baseParams,
        status: 0, // 未完成
        flow: '7', // 上门回收
      }
      break

    default:
      throw new Error(`Unsupported flow category: ${category}`)
  }

  return fetchFlowList(requestParams)
}

// 为了保持向后兼容性，保留原有的函数名，但内部调用新的统一函数
/**
 * 获取待预约安装列表
 * @param params 请求参数
 * @returns 待预约安装数据
 */
export async function fetchPendingInstallationReservation(params?: Omit<FlowParams, 'flow'>) {
  return fetchFlowByCategory(FlowCategory.PENDING_INSTALLATION_RESERVATION, params)
}

/**
 * 获取待预约回收列表
 * @param params 请求参数
 * @returns 待预约回收数据
 */
export async function fetchPendingRecyclingReservation(params?: Omit<FlowParams, 'flow'>) {
  return fetchFlowByCategory(FlowCategory.PENDING_RECYCLING_RESERVATION, params)
}

/**
 * 获取待勘查列表
 * @param params 请求参数
 * @returns 待勘查数据
 */
export async function fetchPendingInspection(params?: Omit<FlowParams, 'flow' | 'status'>) {
  return fetchFlowByCategory(FlowCategory.PENDING_INSPECTION, params)
}

/**
 * 获取待安装列表
 * @param params 请求参数
 * @returns 待安装数据
 */
export async function fetchPendingInstallation(params?: Omit<FlowParams, 'flow' | 'status'>) {
  return fetchFlowByCategory(FlowCategory.PENDING_INSTALLATION, params)
}

/**
 * 获取待回收列表
 * @param params 请求参数
 * @returns 待回收数据
 */
export async function fetchPendingRecycling(params?: Omit<FlowParams, 'flow' | 'status'>) {
  return fetchFlowByCategory(FlowCategory.PENDING_RECYCLING, params)
}
