/**
 * 流程监控相关 API
 */

import type { FlowItem, FlowParams } from './types'
import { http } from '@shencom/request'
import { url } from './config'

/**
 * 获取流程监控列表
 * @param params 请求参数
 * @returns 流程监控响应数据
 */
export async function fetchFlowIndex(params?: FlowParams) {
  const requestParams = {
    size: 10,
    page: 0,
    ...params,
  }

  return http.post<SC.API.IndexInterface<FlowItem>>(`${url}/monitor/order/index`, requestParams)
}

/**
 * 获取特定流程类型的数据
 * @param flow 流程类型
 * @param params 额外的请求参数
 * @returns 流程数据
 */
export async function fetchFlowByType(
  flow: number,
  params?: Omit<FlowParams, 'flow'>,
) {
  const requestParams = {
    size: 5,
    page: 0,
    ...params,
    flow,
  }

  return fetchFlowIndex(requestParams)
}

/**
 * 获取待预约安装列表（安装预约）
 * @param params 请求参数
 * @returns 待预约安装数据
 */
export async function fetchPendingInstallationReservation(params?: Omit<FlowParams, 'flow'>) {
  return fetchFlowByType(1, params) // 安装预约
}

/**
 * 获取待预约回收列表（回收预约）
 * @param params 请求参数
 * @returns 待预约回收数据
 */
export async function fetchPendingRecyclingReservation(params?: Omit<FlowParams, 'flow'>) {
  return fetchFlowByType(6, params) // 回收预约
}

/**
 * 获取待勘查列表（现场勘察）
 * @param params 请求参数
 * @returns 待勘查数据
 */
export async function fetchPendingInspection(params?: Omit<FlowParams, 'flow'>) {
  return fetchFlowByType(2, params) // 现场勘察
}

/**
 * 获取待安装列表（上门安装）
 * @param params 请求参数
 * @returns 待安装数据
 */
export async function fetchPendingInstallation(params?: Omit<FlowParams, 'flow'>) {
  return fetchFlowByType(3, params) // 上门安装
}

/**
 * 获取待回收列表（上门回收）
 * @param params 请求参数
 * @returns 待回收数据
 */
export async function fetchPendingRecycling(params?: Omit<FlowParams, 'flow'>) {
  return fetchFlowByType(7, params) // 上门回收
}
